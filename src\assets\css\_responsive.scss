// ===== 响应式适配样式 =====

// === 容器最大宽度 ===
.container {
  width: 100%;
  margin: 0 auto;
  padding: 0 16px;
  
  @include respond-to(sm) {
    max-width: 540px;
  }
  
  @include respond-to(md) {
    max-width: 720px;
  }
  
  @include respond-to(lg) {
    max-width: 960px;
    padding: 0 20px;
  }
  
  @include respond-to(xl) {
    max-width: 1140px;
    padding: 0 24px;
  }
  
  @include respond-to(xxl) {
    max-width: 1320px;
  }
}

// === 响应式网格系统 ===
.row {
  display: flex;
  flex-wrap: wrap;
  margin: 0 -8px;
  
  @include respond-to(md) {
    margin: 0 -12px;
  }
  
  @include respond-to(lg) {
    margin: 0 -16px;
  }
}

.col {
  flex: 1;
  padding: 0 8px;
  
  @include respond-to(md) {
    padding: 0 12px;
  }
  
  @include respond-to(lg) {
    padding: 0 16px;
  }
}

// 生成响应式列类
@for $i from 1 through 12 {
  .col-#{$i} {
    flex: 0 0 percentage($i / 12);
    max-width: percentage($i / 12);
  }
}

@each $breakpoint in map-keys($breakpoints) {
  @include respond-to($breakpoint) {
    @for $i from 1 through 12 {
      .col-#{$breakpoint}-#{$i} {
        flex: 0 0 percentage($i / 12);
        max-width: percentage($i / 12);
      }
    }
  }
}

// === 响应式字体大小 ===
.text-responsive {
  font-size: 14px;
  
  @include respond-to(sm) {
    font-size: 15px;
  }
  
  @include respond-to(md) {
    font-size: 16px;
  }
  
  @include respond-to(lg) {
    font-size: 17px;
  }
  
  @include respond-to(xl) {
    font-size: 18px;
  }
}

.title-responsive {
  font-size: 20px;
  
  @include respond-to(sm) {
    font-size: 22px;
  }
  
  @include respond-to(md) {
    font-size: 24px;
  }
  
  @include respond-to(lg) {
    font-size: 28px;
  }
  
  @include respond-to(xl) {
    font-size: 32px;
  }
}

// === 响应式间距 ===
.section-padding {
  padding: $spacing-base;
  
  @include respond-to(md) {
    padding: $spacing-lg;
  }
  
  @include respond-to(lg) {
    padding: $spacing-xl;
  }
  
  @include respond-to(xl) {
    padding: $spacing-xxl;
  }
}

.section-margin {
  margin: $spacing-base 0;
  
  @include respond-to(md) {
    margin: $spacing-lg 0;
  }
  
  @include respond-to(lg) {
    margin: $spacing-xl 0;
  }
  
  @include respond-to(xl) {
    margin: $spacing-xxl 0;
  }
}

// === 响应式卡片 ===
.card-responsive {
  padding: $spacing-base;
  border-radius: $border-radius-base;
  
  @include respond-to(md) {
    padding: $spacing-lg;
    border-radius: $border-radius-lg;
  }
  
  @include respond-to(lg) {
    padding: $spacing-xl;
  }
}

// === 响应式按钮 ===
.btn-responsive {
  padding: $spacing-sm $spacing-base;
  font-size: $font-size-sm;
  
  @include respond-to(md) {
    padding: $spacing-base $spacing-lg;
    font-size: $font-size-base;
  }
  
  @include respond-to(lg) {
    padding: $spacing-base $spacing-xl;
    font-size: $font-size-lg;
  }
}

// === 响应式表格 ===
.table-responsive {
  overflow-x: auto;
  -webkit-overflow-scrolling: touch;
  
  @include respond-to(md) {
    overflow-x: visible;
  }
  
  table {
    min-width: 600px;
    
    @include respond-to(md) {
      min-width: auto;
    }
  }
}

// === 响应式导航 ===
.nav-responsive {
  .nav-item {
    padding: $spacing-sm;
    font-size: $font-size-sm;
    
    @include respond-to(md) {
      padding: $spacing-base;
      font-size: $font-size-base;
    }
    
    @include respond-to(lg) {
      padding: $spacing-base $spacing-lg;
    }
  }
}

// === 响应式表单 ===
.form-responsive {
  .form-group {
    margin-bottom: $spacing-base;
    
    @include respond-to(md) {
      margin-bottom: $spacing-lg;
    }
  }
  
  .form-control {
    padding: $spacing-sm;
    font-size: $font-size-sm;
    
    @include respond-to(md) {
      padding: $spacing-base;
      font-size: $font-size-base;
    }
  }
  
  .form-label {
    font-size: $font-size-sm;
    margin-bottom: $spacing-xs;
    
    @include respond-to(md) {
      font-size: $font-size-base;
      margin-bottom: $spacing-sm;
    }
  }
}

// === 响应式图片 ===
.img-responsive {
  max-width: 100%;
  height: auto;
  display: block;
}

// === 响应式视频 ===
.video-responsive {
  position: relative;
  padding-bottom: 56.25%; // 16:9 aspect ratio
  height: 0;
  overflow: hidden;
  
  iframe,
  object,
  embed,
  video {
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
  }
}

// === 响应式隐藏/显示 ===
// 在小屏幕上隐藏
.hidden-xs {
  @media (max-width: map-get($breakpoints, sm) - 1px) {
    display: none !important;
  }
}

.hidden-sm {
  @include respond-to(sm) {
    @media (max-width: map-get($breakpoints, md) - 1px) {
      display: none !important;
    }
  }
}

.hidden-md {
  @include respond-to(md) {
    @media (max-width: map-get($breakpoints, lg) - 1px) {
      display: none !important;
    }
  }
}

.hidden-lg {
  @include respond-to(lg) {
    @media (max-width: map-get($breakpoints, xl) - 1px) {
      display: none !important;
    }
  }
}

// 只在特定屏幕尺寸显示
.visible-xs {
  display: none !important;
  
  @media (max-width: map-get($breakpoints, sm) - 1px) {
    display: block !important;
  }
}

.visible-sm {
  display: none !important;
  
  @include respond-to(sm) {
    @media (max-width: map-get($breakpoints, md) - 1px) {
      display: block !important;
    }
  }
}

.visible-md {
  display: none !important;
  
  @include respond-to(md) {
    @media (max-width: map-get($breakpoints, lg) - 1px) {
      display: block !important;
    }
  }
}

.visible-lg {
  display: none !important;
  
  @include respond-to(lg) {
    display: block !important;
  }
}
