<template>
<<<<<<< HEAD
  <div class="activity-create-container" ref="public">
    <!-- 页面头部 -->
    <div class="page-header">
      <div class="header-content">
        <template v-if="isEdit">
          <el-button
            type="text"
            icon="el-icon-arrow-left"
            class="back-btn"
            @click="$router.go(-1)"
          >
            返回
          </el-button>
        </template>
        <div class="page-title">
          <h1>{{ isEdit ? '编辑活动' : '创建活动' }}</h1>
          <p class="page-subtitle">{{ isEdit ? '修改活动信息和配置' : '填写活动基本信息，设置报名规则' }}</p>
        </div>
      </div>
    </div>

    <!-- 主要内容区域 -->
    <div class="main-content">
      <!-- 进度指示器 -->
      <div class="progress-steps" :class="{ 'floating': isFloating }" ref="progressSteps">
        <div
          class="step"
          :class="{ 'active': currentStep >= 1, 'completed': currentStep > 1 }"
          @click="scrollToSection('basic-info')"
        >
          <div class="step-icon">
            <i v-if="currentStep > 1" class="el-icon-check"></i>
            <span v-else>1</span>
          </div>
          <div class="step-title">基本信息</div>
        </div>
        <div class="step-line" :class="{ 'completed': currentStep > 1 }"></div>
        <div
          class="step"
          :class="{ 'active': currentStep >= 2, 'completed': currentStep > 2 }"
          @click="scrollToSection('activity-content')"
        >
          <div class="step-icon">
            <i v-if="currentStep > 2" class="el-icon-check"></i>
            <span v-else>2</span>
          </div>
          <div class="step-title">活动内容</div>
        </div>
        <div class="step-line" :class="{ 'completed': currentStep > 2 }"></div>
        <div
          class="step"
          :class="{ 'active': currentStep >= 3, 'completed': currentStep > 3 }"
          @click="scrollToSection('signup-info')"
        >
          <div class="step-icon">
            <i v-if="currentStep > 3" class="el-icon-check"></i>
            <span v-else>3</span>
          </div>
          <div class="step-title">报名设置</div>
        </div>
      </div>

      <!-- 表单区域 -->
      <div class="form-sections">
        <!-- 基本信息模块 -->
        <div class="form-section" id="basic-info">
          <div class="section-header">
            <div class="section-icon">
              <i class="el-icon-edit-outline"></i>
            </div>
            <div class="section-title">
              <h2>基本信息</h2>
              <p>设置活动的基本信息，包括名称、时间、地点等</p>
            </div>
          </div>
          <div class="section-content">
            <PublicForm
              :publicData="publicData"
              :rewardDTOList="rewardDTOList"
              :captainList="captainList"
              :departmentTree="departmentTree"
              :allDepartmentList="allDepartmentList"
              :rules="rules"
              @addPrice="addPrice"
              @modifyPrice="modifyPrice"
              @deletePrice="deletePrice"
              @getCaps="getCaps"
              @getDepts="getDepts"
              :oldDepartsArrary="oldDepartsArrary"
              :selectedCaptains="selectedCaptains"
              ref="publicForm"
            />
          </div>
        </div>

        <!-- 活动内容模块 -->
        <div class="form-section" id="activity-content">
          <div class="section-header">
            <div class="section-icon">
              <i class="el-icon-document"></i>
            </div>
            <div class="section-title">
              <h2>活动内容</h2>
              <p>添加活动的详细介绍和相关内容模块</p>
            </div>
          </div>
          <div class="section-content">
            <ActivityModuleForm
              :activityModuleList="publicData.activityContentList"
              :rules="rules"
              ref="moduleCom"
              @addActivityModule="addActivityModule"
              @deleteActivityModule="deleteActivityModule"
            />
          </div>
        </div>

        <!-- 报名信息模块 -->
        <div class="form-section" id="signup-info">
          <div class="section-header">
            <div class="section-icon">
              <i class="el-icon-user"></i>
            </div>
            <div class="section-title">
              <h2>报名设置</h2>
              <p>配置报名表单字段和相关设置</p>
            </div>
          </div>
          <div class="section-content">
            <SignUpInfoForm
              :signUpFormList="fieldDTOList"
              :isAllowedBring="publicData.activityDetail.isAllowedBring"
              :isTeam="publicData.activityDetail.isTeam"
              @addSignUpField="addSignUpField"
              @deleteSignUpField="deleteSignUpField"
              :rules="rules"
            />
          </div>
        </div>
      </div>
    </div>

    <!-- 底部操作栏 -->
    <div class="footer-actions">
      <div class="actions-content">
        <div class="left-actions">
          <el-button
            v-if="!publicData.status && $store.state.activityData !== null"
            @click="clearStore"
            icon="el-icon-delete"
          >
            清除缓存
          </el-button>
        </div>
        <div class="right-actions">
          <el-button
            size="large"
            @click="$router.go(-1)"
          >
            取消
          </el-button>
          <el-button
            v-if="
              !publicData.status ||
              publicData.status === 1 ||
              publicData.status === 2
            "
            type="primary"
            size="large"
            @click="publicActivityFromPc"
            :loading="isSubmit"
            icon="el-icon-check"
          >
            {{ publicData.status ? "保存修改" : "发布活动" }}
          </el-button>
        </div>
      </div>
=======
  <div ref="public">
    <template v-if="isEdit">
      <i
        class="el-icon-back"
        style="fontsize: 25px; padding-left: 10px; color: grey; cursor: pointer"
        @click="$router.go(-1)"
      ></i>
      <el-divider />
    </template>
    <Title :titleConfig="basicInfo" />
    <!-- 基本信息模块表单 -->
    <PublicForm
      :publicData="publicData"
      :rewardDTOList="rewardDTOList"
      :captainList="captainList"
      :departmentTree="departmentTree"
      :allDepartmentList="allDepartmentList"
      :rules="rules"
      @addPrice="addPrice"
      @modifyPrice="modifyPrice"
      @deletePrice="deletePrice"
      @getCaps="getCaps"
      @getDepts="getDepts"
      :oldDepartsArrary="oldDepartsArrary"
      :selectedCaptains="selectedCaptains"
      ref="publicForm"
    />
    <el-divider />
    <Title :titleConfig="moduleInfo" :top="60" />
    <ActivityModuleForm
      :activityModuleList="publicData.activityContentList"
      :rules="rules"
      ref="moduleCom"
      @addActivityModule="addActivityModule"
      @deleteActivityModule="deleteActivityModule"
    />
    <el-divider />
    <Title :titleConfig="signUpInfo" :top="60" />
    <SignUpInfoForm
      :signUpFormList="fieldDTOList"
      :isAllowedBring="publicData.activityDetail.isAllowedBring"
      :isTeam="publicData.activityDetail.isTeam"
      @addSignUpField="addSignUpField"
      @deleteSignUpField="deleteSignUpField"
      :rules="rules"
    />
    <el-divider />
    <div class="public">
      <el-button
        v-if="!publicData.status && $store.state.activityData !== null"
        @click="clearStore"
      >
        清除数据缓存
      </el-button>
      <el-button
        v-if="
          !publicData.status ||
          publicData.status === 1 ||
          publicData.status === 2
        "
        type="primary"
        size="large"
        @click="publicActivityFromPc"
        :loading="isSubmit"
        >{{ publicData.status ? "保存" : "发布" }}</el-button
      >
>>>>>>> bfda4301 (fix:更新代码，去除百度jquery文件，修复部分人无法登录问题。)
    </div>
    <!-- 设置奖品弹窗 -->
    <el-dialog
      :title="priceTitle"
      v-if="dialogFormVisible"
      :visible.sync="dialogFormVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="cancel"
    >
      <PriceForm :priceField="priceField" :rules="rules" ref="pirceForm" />
      <div slot="footer" class="dialog-footer">
        <el-button @click="cancel">取 消</el-button>
        <el-button type="primary" @click="submit(priceTitle)">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import Title from "../../../components/title";
import PublicForm from "./publicForm";
import ActivityModuleForm from "./activityModuleForm";
import SignUpInfoForm from "./signUpInfoForm";
import PriceForm from "./priceForm";
import {
  getCaptainListFromPc,
  getSignUpFieldFromPc,
  getDeptTreeFromPc,
  getDeptListFromPc,
} from "../../../api/signUp/index";
import {
  publicActivityFromPc,
  getActivityDetailFromPc,
} from "../../../api/activity/index";
import { getRewardListFromPc } from "../../../api/price/index";
import { getTeamLeaderFromPc } from "../../../api/team/index";
import requestMixin from "../../../mixin/requestMixin";
import { convertTimeWithMinutes } from "../../../utils";
export default {
  components: {
    Title,
    PublicForm,
    ActivityModuleForm,
    SignUpInfoForm,
    PriceForm,
  },
  mixins: [requestMixin],
  inject: ["reload"],
  data() {
    return {
      // 滚动相关数据
      isFloating: false,
      currentStep: 1,
      progressStepsOriginalTop: 0,

      // 屏幕方向监听器
      orientationHandler: null,

      parentIdList: [],
      basicInfo: {
        fontSize: 20,
        title: "基本信息",
      },
      moduleInfo: {
        fontSize: 20,
        title: "活动内容模块",
      },
      signUpInfo: {
        fontSize: 20,
        title: "报名信息",
      },
      lineConfig: {
        border: "5px solid red",
      },
      publicData: {
        activityDetail: {},
      },
      // 奖品列表
      rewardDTOList: [],
      teamDTOList: [],
      fieldDTOList: [],
      // 在删除字段时不删除，将对应item的deleted字段置为1;
      deleteFieldDTOList: [],
      // 在删除活动内容时，将对应item的deleted字段置为1;
      deleteActivityContentList: [],
      // 删除队长，将对应item的deleted字段置为1;
      deleteCapsList: [],
      // 删除奖品
      deletePriceList: [],
      captainList: [],
      // 部门树
      departmentTree: [],
      // 全部部门列表
      allDepartmentList: [],
      dialogFormVisible: false,
      isSubmit: false,
      priceField: {
        rewardLevel: "",
        rewardName: "",
        number: 0,
      },
      priceTitle: "",
      rules: {
        actName: [
          { required: true, message: "请输入活动主题", trigger: "blur" },
          {
            min: 1,
            max: 50,
            message: "活动主题在1-50个字符以内",
            trigger: "blur",
          },
<<<<<<< HEAD
        ],
        actType: [
          { required: true, message: "请选择活动类型", trigger: "change" },
=======
>>>>>>> bfda4301 (fix:更新代码，去除百度jquery文件，修复部分人无法登录问题。)
        ],
        enlistStartTime: [
          { required: true, message: "请输入报名开始时间", trigger: "blur" },
        ],
        enlistEndTime: [
          { required: true, message: "请输入报名结束时间", trigger: "blur" },
        ],
        actNum: [
          { required: true, message: "请输入报名人数", trigger: "blur" },
          {
            min: 1,
            max: 1000,
            message: "人数不符合要求",
            trigger: "blur",
            type: "number",
          },
          {
            validator: (rule, value, callback) => {
              if (value < this.publicData.participationNum) {
                callback(new Error("报名人数不得少于当前报名成功人数"));
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        // 携带人数的限制
        "activityDetail.bringRelativesNum": [
          { required: true, message: "请输入允许携带人数", trigger: "blur" },
          {
            min: 1,
            message: "允许携带人数最少为1",
            trigger: "blur",
            type: "number",
          },
          // 活动开始报名后，对携带亲属人数修改限制
          {
            validator: (rule, value, callback) => {
              if (this.publicData.status === 2) {
                if (value < this.preBringNum) {
                  callback(new Error("不得少于当前允许人数"));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        actStartTime: [
          { required: true, message: "请输入活动开始时间", trigger: "blur" },
        ],
        actEndTime: [
          { required: true, message: "请输入活动结束时间", trigger: "blur" },
        ],
        address: [
          { required: true, message: "请输入活动地址", trigger: "blur" },
        ],
        rewardLevel: [
          {
            required: true,
            message: "请输入奖项名称，例如一等奖",
            trigger: "blur",
          },
        ],
        rewardName: [
          { required: true, message: "请输入奖品名称", trigger: "blur" },
        ],
        number: [
          { required: true, message: "请输入奖品数量", trigger: "blur" },
          { min: 1, message: "数量至少为1", trigger: "blur", type: "number" },
        ],
        "activityDetail.enlistCost": [
          {
            required: true,
            min: 0,
            message: "报名费用最少为0元",
            trigger: "blur",
            type: "number",
          },
        ],
        // 队伍最多人数限制
        "activityDetail.teamPlayersMax": [
          { required: true, message: "请输入队伍最大人数", trigger: "blur" },
          {
            min: 2,
            message: "队伍最大数最少为2",
            trigger: "blur",
            type: "number",
          },
          {
            max: 500,
            message: "队伍最大数不得超过500",
            trigger: "blur",
            type: "number",
          },
          {
            validator: (rule, value, callback) => {
              if (this.publicData.status === 2) {
                if (value < this.publicData.activityDetail.teamPlayersMin) {
                  callback(new Error("不得少于最小人数"));
                } else if (value < this.preTeamPlayersMax) {
                  callback(new Error("不得少于当前队伍最大人数"));
                } else if (
                  value > Math.ceil(this.publicData.actNum / this.teamLength)
                ) {
                  callback(new Error("队伍数不得少于当前队伍数"));
                } else {
                  callback();
                }
              } else {
                callback();
              }
            },
            trigger: "blur",
          },
        ],
        fieldName: [
          { required: true, message: "请输入或选择字段名称", trigger: "blur" },
        ],
        activityContentTitle: [
          { required: true, message: "请输入活动内容标题", trigger: "blur" },
        ],
        activityContentDetail: [
          {
            required: true,
            message: "请输入活动内容具体介绍",
            trigger: "blur",
          },
        ],
      },
      // 已选择队长的id数组，用于数据回显
      selectedCaptains: [],
      // 选择的队长名单
      selectedCaps: [],
      // 已被选中的部门id列表(旧部门列表)
      oldSelectedDeparts: [],
      // 部门选项拼上父级id后,用于数据回显
      oldDepartsArrary: [],
      // 选中的新部门列表
      newSelectedDeparts: [],
      // 用于判断用户是否有操作选择队长操作
      selectedStatus: false,
      // 用于标识新增的奖品信息（在发布前删除某条信息时使用）
      priceId: 0,
      isEdit: false,
      // 当前亲属允许人数
      preBringNum: 0,
      // 当前队伍最大数
      preTeamPlayersMax: 0,
      // 当前活动队伍数
      teamLength: 0,
<<<<<<< HEAD
      // 当前活动类型
      actType:0,
=======
>>>>>>> bfda4301 (fix:更新代码，去除百度jquery文件，修复部分人无法登录问题。)
    };
  },
  created() {
    /*eslint-disable */
    if (this.$route.params.id) {
      this.isEdit = true;
      this.getDetail(this.$route.params.id);
      this.getFields(this.$route.params.id);
      this.getRewardListFromPc(this.$route.params.id);
      this.getTeamLeaderFromPc(this.$route.params.id);
    } else {
      // 获取之前填写的数据
      const data = this.$store.state.activityData;
      console.log(data);
      this.publicData = {
        actName: (data && data.activityDTO && data.activityDTO.actName) || "",
        enlistStartTime:
          (data && data.activityDTO && data.activityDTO.enlistStartTime) || "",
        enlistEndTime:
          (data && data.activityDTO && data.activityDTO.enlistEndTime) || "",
        actNum: (data && data.activityDTO && data.activityDTO.actNum) || "",
        actImg: (data && data.activityDTO && data.activityDTO.actImg) || "",
        actStartTime:
          (data && data.activityDTO && data.activityDTO.actStartTime) || "",
        actEndTime:
          (data && data.activityDTO && data.activityDTO.actEndTime) || "",
<<<<<<< HEAD
        // 活动类型：0=徒步活动，1=自定义活动
        actType: (data && data.activityDTO && data.activityDTO.actType !== undefined) ? data.activityDTO.actType : 0,
=======
>>>>>>> bfda4301 (fix:更新代码，去除百度jquery文件，修复部分人无法登录问题。)
        activityDetail: (data &&
          data.activityDTO &&
          data.activityDTO.activityDetail) || {
          agreement: "",
          enclosure: "",
          enlistCost: "",
          isDraw: 0,
          isExamine: 1,
          isAllowedBring: 0,
          // 允许携带人数
          bringRelativesNum: "",
          isAllowedAlternate: 0,
          // 允许候补人数
          allowedAlternateNum: 0,
          isSignupNotice: 0,
          isNoticeCreator: 0,
          isLuckDraw: 0,
          isTeam: 0,
          // 队伍人数最少限制为1
          teamPlayersMax: 2,
          teamPlayersMin: 1,
        },
        // 报名成功人数
        // participationNum:
        // (data && data.activityDTO && data.activityDTO.participationNum) || 1,
        activityImage:
          (data && data.activityDTO && data.activityDTO.activityImage) || "",
        address: (data && data.activityDTO && data.activityDTO.address) || "",
        // activityContentList: (data &&
        //   data.activityDTO &&
        //   data.activityDTO.activityContentList) || [
        //   {
        //     aid: Math.floor(Math.random() * 100000),
        //     title: "",
        //     content: "",
        //   },
        // ],
      };
      this.preBringNum = this.publicData.activityDetail.bringRelativesNum;
      this.preTeamPlayersMax = this.publicData.activityDetail.teamPlayersMax;
      if (data && data.activityDTO && data.activityDTO.activityContentList) {
        // 为publicData对象添加一个新'activityContentList'属性
        this.$set(
          this.publicData,
          "activityContentList",
          data.activityDTO.activityContentList
        );
      } else {
        this.$set(this.publicData, "activityContentList", [
          { aid: Math.floor(Math.random() * 100000), title: "", content: "" },
        ]);
      }
      (this.rewardDTOList = (data && data.rewardDTOList) || []),
        (this.teamDTOList = (data && data.teamDTOList) || []),
        // this.selectedStatus = data && data.teamDTOList && data.teamDTOList.length !== 0 ? true : false;
        (this.fieldDTOList = (data && data.fieldDTOList) || [
          { fieldName: "姓名", isRequest: 1, must: true, deleted: 0 },
          // { fieldName: "性别", isRequest: 1, must: true },
          { fieldName: "电话", isRequest: 1, must: true, deleted: 0 },
          // { fieldName: "上车点", isRequest: 1, must: true },
          // TODO 添加判断是否显示
          { fieldName: "上车地点(市区、公司)", isRequest: 0, must: true, deleted: 0 },
          { fieldName: "部门", isRequest: 0, must: true, deleted: 0 },
          { fieldName: "备注", isRequest: 1, must: true, deleted: 0 },
          { fieldName: "亲属人数", isRequest: 0, must: true, deleted: 0 },
          { fieldName: "亲属备注", isRequest: 0, must: false, deleted: 0 },
          { fieldName: "队伍", isRequest: 1, must: true, deleted: 0 },
        ]);
      if (this.teamDTOList.length > 0) {
        // 队长名单
        if (this.publicData.activityDetail.isTeam === 1) {
          this.teamDTOList.forEach((item) => {
            this.selectedCaptains.push(item.teamLeaderId);
            this.oldSelectedDeparts = [];
          });
          // 队伍名单
        } else if (this.publicData.activityDetail.isTeam === 2) {
          this.teamDTOList.forEach((item) => {
            this.oldSelectedDeparts.push(item.departId);
            this.selectedCaptains = [];
          });
        }
      }
    }
  },
  mounted() {
    this.getCaptainListFromPc();
    this.getDeptTreeFromPc();
    this.getDeptListFromPc();

    // 初始检查移动设备横屏状态
    this.checkInitialOrientation();

    // 监听屏幕方向变化
    this.addOrientationListener();

    // 初始化滚动监听
    this.$nextTick(() => {
      const progressSteps = this.$refs.progressSteps;
      if (progressSteps) {
        this.progressStepsOriginalTop = progressSteps.offsetTop;
        console.log('Progress steps original top:', this.progressStepsOriginalTop);
      }

      // 尝试多种滚动容器选择器
      let scrollContainer = document.querySelector('.content-wrapper');
      if (!scrollContainer) {
        scrollContainer = document.querySelector('.main-content');
      }
      if (!scrollContainer) {
        scrollContainer = document.querySelector('.activity-create-container');
      }
      if (!scrollContainer) {
        // 如果都找不到，使用window
        console.log('使用window作为滚动容器');
        window.addEventListener('scroll', this.handleScrollWindow);
        this.updateCurrentStepWindow();
      } else {
        console.log('找到滚动容器:', scrollContainer.className);
        scrollContainer.addEventListener('scroll', this.handleScroll);
        this.updateCurrentStep(scrollContainer);
      }
    });
  },
  beforeDestroy() {
    // 移除滚动监听器
    const scrollContainer = document.querySelector('.content-wrapper');
    if (scrollContainer) {
      scrollContainer.removeEventListener('scroll', this.handleScroll);
    }
    // 也移除window滚动监听器
    window.removeEventListener('scroll', this.handleScrollWindow);

    // 移除屏幕方向监听器
    this.removeOrientationListener();
  },
  methods: {
<<<<<<< HEAD
    // 滚动监听方法
    handleScroll(event) {
      const progressSteps = this.$refs.progressSteps;
      if (!progressSteps) return;

      // 获取滚动容器
      const scrollContainer = event.target;
      const scrollTop = scrollContainer.scrollTop;

      console.log('滚动监听:', {
        scrollTop,
        originalTop: this.progressStepsOriginalTop,
        shouldFloat: scrollTop > this.progressStepsOriginalTop + 100,
        isFloating: this.isFloating
      });

      // 判断是否需要浮动
      if (scrollTop > this.progressStepsOriginalTop + 100) {
        this.isFloating = true;
        console.log('设置浮窗状态: true');
      } else {
        this.isFloating = false;
        console.log('设置浮窗状态: false');
      }

      // 计算当前步骤
      this.updateCurrentStep(scrollContainer);
    },

    // 更新当前步骤
    updateCurrentStep(scrollContainer) {
      const sections = [
        { id: 'basic-info', step: 1 },
        { id: 'activity-content', step: 2 },
        { id: 'signup-info', step: 3 }
      ];

      const scrollTop = scrollContainer ? scrollContainer.scrollTop : 0;
      const containerHeight = scrollContainer ? scrollContainer.clientHeight : window.innerHeight;

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = document.getElementById(sections[i].id);
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;

          // 如果section在视窗中可见（至少30%）
          if (scrollTop + containerHeight * 0.3 >= sectionTop &&
              scrollTop < sectionTop + sectionHeight) {
            this.currentStep = sections[i].step;
            break;
          }
        }
      }
    },

    // window滚动监听方法
    handleScrollWindow() {
      const progressSteps = this.$refs.progressSteps;
      if (!progressSteps) return;

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;

      // 判断是否需要浮动
      if (scrollTop > this.progressStepsOriginalTop + 100) {
        this.isFloating = true;
      } else {
        this.isFloating = false;
      }

      // 计算当前步骤
      this.updateCurrentStepWindow();
    },

    // window更新当前步骤
    updateCurrentStepWindow() {
      const sections = [
        { id: 'basic-info', step: 1 },
        { id: 'activity-content', step: 2 },
        { id: 'signup-info', step: 3 }
      ];

      const scrollTop = window.pageYOffset || document.documentElement.scrollTop;
      const windowHeight = window.innerHeight;

      for (let i = sections.length - 1; i >= 0; i--) {
        const section = document.getElementById(sections[i].id);
        if (section) {
          const sectionTop = section.offsetTop;
          const sectionHeight = section.offsetHeight;

          // 如果section在视窗中可见（至少30%）
          if (scrollTop + windowHeight * 0.3 >= sectionTop &&
              scrollTop < sectionTop + sectionHeight) {
            this.currentStep = sections[i].step;
            break;
          }
        }
      }
    },

    // 滚动到指定section
    scrollToSection(sectionId) {
      const section = document.getElementById(sectionId);
      if (section) {
        // 尝试多种滚动方式
        let scrollContainer = document.querySelector('.content-wrapper');
        if (!scrollContainer) {
          scrollContainer = document.querySelector('.main-content');
        }

        if (scrollContainer) {
          const offsetTop = section.offsetTop - 120;
          scrollContainer.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });
        } else {
          // 使用window滚动
          const offsetTop = section.offsetTop - 120;
          window.scrollTo({
            top: offsetTop,
            behavior: 'smooth'
          });
        }
      }
    },

    // 修改部门选中数组，用于数据回显
    getDepartIds() {
      const listId = [];
      this.oldSelectedDeparts.forEach((item) => {
        this.getParentIds(item);
        listId.push(this.parentIdList);
        this.parentIdList = [];
      });
=======
    // 修改部门选中数组，用于数据回显
    getDepartIds() {
      const listId = [];
      this.oldSelectedDeparts.forEach((item) => {
        this.getParentIds(item);
        listId.push(this.parentIdList);
        this.parentIdList = [];
      });
>>>>>>> bfda4301 (fix:更新代码，去除百度jquery文件，修复部分人无法登录问题。)
      this.oldDepartsArrary = listId;
    },
    // 获取部门parentIds
    getParentIds(key) {
      if (key !== 0) {
        this.allDepartmentList.forEach((item) => {
          if (item.id === key) {
            this.parentIdList.unshift(item.id);
            this.getParentIds(item.parentId);
          }
        });
      } else {
        return;
      }
    },
    // 获取当前活动的队长信息
    async getTeamLeaderFromPc(id) {
      const [err, res] = await this.request(getTeamLeaderFromPc, {
        actId: id,
      });
      if (err) {
        return;
      }
      if (res.data.data !== 0) {
        this.teamDTOList = res.data.data;
      }
      this.teamLength = res.data.data.length;
      if (this.teamDTOList.length !== 0) {
        this.teamDTOList.forEach((item) => {
          this.selectedCaptains.push(item.teamLeaderId);
          this.oldSelectedDeparts.push(item.departId);
        });
      }
    },
    // 获取奖品信息
    async getRewardListFromPc(id) {
      const [err, res] = await this.request(getRewardListFromPc, {
        actId: id,
        currentPage: -1,
        pageSize: 100,
      });
      if (err) {
        return;
      }
      this.rewardDTOList = res.data.data;
      this.rewardDTOList.map((item) => {
        item.pid = item.id;
      });
    },
    // 获取字段
    async getFields(id) {
      const [err, res] = await this.request(getSignUpFieldFromPc, {
        actId: id,
      });
      if (err) {
        return;
      }
      this.fieldDTOList = res.data.data;
      console.log
    },
    // 获取活动详细信息
    async getDetail(id) {
      const [err, res] = await this.request(getActivityDetailFromPc, {
        actId: id,
      });
      if (err) {
        return;
      }
      this.publicData = res.data.data;
      this.preBringNum = this.publicData.activityDetail.bringRelativesNum;
      this.preTeamPlayersMax = this.publicData.activityDetail.teamPlayersMax;
      console.log(this.publicData);
    },
    // 获取已选择的队长名单
    getCaps(list) {
      this.selectedCaps = list;
      this.selectedStatus = true;
    },
    getDepts(list) {
      this.newSelectedDeparts = list;
      this.selectedStatus = true;
    },
    // 获取队长池名单
    async getCaptainListFromPc() {
      const [err, res] = await this.request(getCaptainListFromPc);
      if (err) {
        return;
      }
      this.captainList = res.data.data;
    },
    // 获取部门树
    async getDeptTreeFromPc() {
      const [err, res] = await this.request(getDeptTreeFromPc);
      if (err) {
        return;
      }
      this.departmentTree = res.data.data;
    },
    //获取部门全部列表
    async getDeptListFromPc() {
      const [err, res] = await this.request(getDeptListFromPc);
      if (err) {
        return;
      }
      this.allDepartmentList = res.data.data;
      this.getDepartIds();
    },
    // 校验活动内容模块
    checkModule() {
      if (this.publicData.activityContentList.length !== 0) {
        return this.$refs.moduleCom.verify();
      }
      return true;
    },
    // 检测是否为移动设备
    isMobileDevice() {
      return /Android|webOS|iPhone|iPad|iPod|BlackBerry|IEMobile|Opera Mini/i.test(navigator.userAgent);
    },

    // 检测是否为竖屏
    isPortraitMode() {
      return window.innerHeight > window.innerWidth;
    },

    // 检测是否为移动设备且为竖屏
    checkMobileOrientation() {
      const isMobile = this.isMobileDevice();
      const isPortrait = this.isPortraitMode();

      if (isMobile && isPortrait) {
        this.$message({
          message: '请横屏操作以获得更好的编辑体验',
          type: 'warning',
          duration: 3000,
          showClose: true,
          customClass: 'mobile-orientation-message'
        });
        return false;
      }
      return true;
    },

    // 初始检查移动设备横屏状态
    checkInitialOrientation() {
      if (this.isMobileDevice()) {
        // 延迟检查，确保页面完全加载
        setTimeout(() => {
          if (this.isPortraitMode()) {
            this.$message({
              message: '建议横屏操作以获得更好的编辑体验',
              type: 'info',
              duration: 4000,
              showClose: true,
              customClass: 'mobile-orientation-message'
            });
          }
        }, 1000);
      }
    },

    // 添加屏幕方向监听器
    addOrientationListener() {
      if (this.isMobileDevice()) {
        // 监听屏幕方向变化
        this.orientationHandler = () => {
          setTimeout(() => {
            if (this.isPortraitMode()) {
              this.$message({
                message: '建议横屏操作以获得更好的编辑体验',
                type: 'info',
                duration: 3000,
                showClose: true,
                customClass: 'mobile-orientation-message'
              });
            }
          }, 500); // 延迟检查，等待屏幕方向变化完成
        };

        // 监听resize事件（屏幕方向变化会触发resize）
        window.addEventListener('resize', this.orientationHandler);

        // 如果支持orientationchange事件，也监听它
        if ('onorientationchange' in window) {
          window.addEventListener('orientationchange', this.orientationHandler);
        }
      }
    },

    // 移除屏幕方向监听器
    removeOrientationListener() {
      if (this.orientationHandler) {
        window.removeEventListener('resize', this.orientationHandler);
        if ('onorientationchange' in window) {
          window.removeEventListener('orientationchange', this.orientationHandler);
        }
        this.orientationHandler = null;
      }
    },

    // 发布活动（编辑活动）同一个接口
    publicActivityFromPc() {
      // 检查移动设备横屏状态
      if (!this.checkMobileOrientation()) {
        return;
      }

      if (this.$refs.publicForm.verify() && this.checkModule()) {
        this.isSubmit = true;
        // 将数字字符串改成number类型
        this.publicData.actNum = Number(this.publicData.actNum);
        this.publicData.activityDetail.teamPlayersMax = Number(
          this.publicData.activityDetail.teamPlayersMax
        );
        this.publicData.activityDetail.teamPlayersMin = Number(
          this.publicData.activityDetail.teamPlayersMin
        );
        this.publicData.activityDetail.enlistCost = Number(
          this.publicData.activityDetail.enlistCost
        );
        this.rewardDTOList.map((item) => {
          item.number = Number(item.number);
          return item;
        });
        // 判断时间格式，如果是object类型，说明时间发生改变，重新转换成 yy-mm-dd hh:mm:ss
        typeof this.publicData.enlistStartTime === "object" &&
          (this.publicData.enlistStartTime = convertTimeWithMinutes(
            this.publicData.enlistStartTime.getTime()
          ));
        typeof this.publicData.enlistEndTime === "object" &&
          (this.publicData.enlistEndTime = convertTimeWithMinutes(
            this.publicData.enlistEndTime.getTime()
          ));
        typeof this.publicData.actStartTime === "object" &&
          (this.publicData.actStartTime = convertTimeWithMinutes(
            this.publicData.actStartTime.getTime()
          ));
        typeof this.publicData.actEndTime === "object" &&
          (this.publicData.actEndTime = convertTimeWithMinutes(
            this.publicData.actEndTime.getTime()
          ));
        // // 删除原有的aid属性（用于标识不同的内容）
        // this.publicData.activityContentList.map((item) => {
        //   delete item.aid;
        //   return item;
        // });
        // 根据must字段值添加isRequired值;
        this.fieldDTOList.map((item) => {
          item.isRequired = item.must === true ? 1 : 0;
          return item;
        });
        const fieldDTOList = this.fieldDTOList.concat(this.deleteFieldDTOList);
        // 设置params属性，只有存在数据的DTO才加入参数中;
        let params = {};
        // 如果活动内容为空或者要删除的活动列表为空说明没有填写数据，删除掉该字段;
        // 循环遍历所有的活动内容，如果没有标题和内容均为空，认为没有添加数据
        if (
          this.isActivityContentEmpty() ||
          (this.deleteActivityContentList.length === 0 &&
            this.publicData.activityContentList.length === 0)
        ) {
          // 后端又重新改了，没有数据也要传改字段
          // delete this.publicData.activityContentList;
          this.publicData.activityContentList = [];
        } else {
          this.publicData.activityContentList =
            this.publicData.activityContentList.concat(
              this.deleteActivityContentList
            );
        }
        params.activityDTO = this.publicData;
        params.activityDTO.deleted = 0;
        // 保持用户选择的活动类型，不强制设置为0
        // params.activityDTO.actType = 0;
        // 报名字段
        if (this.fieldDTOList.length !== 0) {
          // params.fieldDTOList = this.fieldDTOList;
          params.fieldDTOList = fieldDTOList;
        }
        // 奖品
        if (
          this.publicData.activityDetail.isLuckDraw === 1 &&
          (this.rewardDTOList.length !== 0 || this.deletePriceList.length !== 0)
        ) {
          params.rewardDTOList = this.rewardDTOList.concat(
            this.deletePriceList
          );
          // 当存在有奖品数据时不抽奖，需要将奖品数据删除
        } else if (
          this.rewardDTOList.length !== 0 ||
          this.deletePriceList.length !== 0
        ) {
          this.rewardDTOList = this.rewardDTOList.concat(this.deletePriceList);
          this.rewardDTOList.map((item) => {
            item.deleted = 1;
            return item;
          });
          params.rewardDTOList = this.rewardDTOList;
        }
        // 队长信息 (新名单长度不为0 或者 旧名单不为 0)
        // 根据之前获取的已选队长信息去匹配当前选中的队长名单，原名单存在，新名单中不存在的将deleted字段置为1
        // 原名单不存在，新名单中存在的组合成一个对象插入DTO中
        if (this.publicData.activityDetail.isTeam === 1) {
          if (
            this.selectedStatus &&
            (this.selectedCaps.length !== 0 || this.selectedCaptains !== 0)
          ) {
            // 根据获取的旧名单去遍历判断新名单中是否存在
            this.teamDTOList.length !== 0 &&
              this.teamDTOList.map((item) => {
                // 判断该队长是否被删除
                if (this.isDeleteCaps(item)) {
                  item.deleted = 1;
                }
              });
            // 新名单存在，遍历名单
            this.selectedCaps.length !== 0 &&
              this.selectedCaps.forEach((item) => {
                // 判断旧名单中是否存在
                if (!this.isSelected(item)) {
                  this.teamDTOList.push(item);
                }
              });
            params.teamDTOList = this.teamDTOList;
            // 多次发布失败时缓存队长信息
          } else if (
            !this.selectedStatus &&
            this.teamDTOList.length !== 0 &&
            this.selectedCaps.length === 0
          ) {
            params.teamDTOList = this.teamDTOList;
          }
        } else if (this.publicData.activityDetail.isTeam === 2) {
          // 新部门列表或者旧部门列表不为空时
          if (
            this.selectedStatus &&
            (this.newSelectedDeparts.length !== 0 ||
              this.teamDTOList.length !== 0)
          ) {
            if (this.teamDTOList.length !== 0) {
              //  存放合并的部门列表
              const list = this.newSelectedDeparts;
              this.teamDTOList.forEach((item) => {
                if (this.isDeleteDepart(item)) {
                  item.deleted = 1;
                  list.push(item);
                }
              });
              params.teamDTOList = list;
            } else {
              params.teamDTOList = this.newSelectedDeparts;
            }
          }
        }

        // 保存已填写的数据，防止在发布失败时数据清空要重新填写
        !this.$route.params.id && this.$store.commit("setActivityData", params);
        if (this.publicData.actNum < this.teamDTOList.length) {
          this.$message.error("队长人数大于实际报名人数，不允许发布");
          this.isSubmit = false;
          return false;
        }
        console.log(params);
        // 判断组队时是否选择了队长
        if (
          params.activityDTO.activityDetail.isTeam === 1 &&
          this.isCapsEmpty()
        ) {
          this.$message.error("组队时队长列表不能为空");
          this.isSubmit = false;
          return false;
        }
        // 发布/保存
        publicActivityFromPc(params).then((res) => {
          if (res.data.status === 200) {
            this.isSubmit = false;
            this.$message.success(
              `${this.publicData.status ? "活动信息更新成功" : "活动发布成功"}`
            );
            // 清除保存的数据
            this.$store.commit("setActivityData", null);
            this.$router.push("/Home/list");
          } else {
            this.isSubmit = false;
            this.$message.error(res.data.message);
            this.reload();
          }
        });
      } else {
        this.$message.error("请填入必填信息");
      }
    },
    // 删除空白活动内容并判断列表是否为空
    isActivityContentEmpty() {
      const len = this.publicData.activityContentList.length;
      const copy = [];
      for (let i = 0; i < len; i++) {
        if (
          this.publicData.activityContentList[i].title !== "" &&
          this.publicData.activityContentList[i].content !== ""
        ) {
          copy.push(this.publicData.activityContentList[i]);
        }
      }
      this.$set(this.publicData, "activityContentList", copy);
      return this.publicData.activityContentList.length === 0;
    },
    // 判断新名单中的队长是否存在于旧名单中
    isSelected(val) {
      if (this.teamDTOList.length === 0) {
        return false;
      }
      try {
        this.teamDTOList.forEach((item) => {
          if (item.teamLeaderId === val.teamLeaderId) {
            throw new Error("stop");
          }
        });
        return false;
      } catch (e) {
        return true;
      }
    },
    // 根据当前选中的队长名单判断之前选中的队长是否发生改变
    isDeleteCaps(item) {
      // 新名单为空，说明全部旧名单被移除
      if (this.selectedCaps.length === 0) {
        return true;
      }
      try {
        // 遍历新名单，如果匹配到id说明该队长没有被删除
        this.selectedCaps.forEach((val) => {
          if (item.teamLeaderId === val.teamLeaderId) {
            throw new Error("stop");
          }
        });
        return true;
      } catch (e) {
        return false;
      }
    },
    // 根据当前选中的部门列表，判断旧部门列表是否被删除
    isDeleteDepart(item) {
      // 新部门列表为空说明，该部门被删除，返回true
      if (this.newSelectedDeparts.length === 0) {
        return true;
      } else {
        try {
          // 遍历新列表，如果匹配到id说明该部门没有被删除，返回false，找不到则表示被删除，返回true
          this.newSelectedDeparts.forEach((val) => {
            if (item.departId === val.departId) {
              throw new Error("stop");
            }
          });
          return true;
        } catch (e) {
          return false;
        }
      }
    },
    // 判断队长列表是否为空
    isCapsEmpty() {
      // 未改变状态且新旧名单都为空
      if (
        !this.selectedStatus &&
        this.selectedCaps.length === 0 &&
        this.teamDTOList.length === 0
      ) {
        console.log("未改变状态且新旧名单都为空");
        return true;
      }
      // 新名单为空，只需要判断旧名单是否也为空或者全部被删除了
      if (this.selectedCaps.length === 0) {
        if (this.teamDTOList.length === 0) {
          return true;
        }
        try {
          this.teamDTOList.forEach((item, index) => {
            if (item.deleted !== 1) {
              throw new Error("stop");
            }
          });
        } catch (e) {
          return false;
        }
        return true;
      }
    },
    addPrice() {
      this.priceTitle = "新增奖品信息";
      this.dialogFormVisible = true;
      this.priceField = {
        rewardLevel: "",
        rewardName: "",
        number: 0,
        pid: this.priceId++,
      };
    },
    modifyPrice(item) {
      this.priceTitle = "修改奖品信息";
      this.dialogFormVisible = true;
      this.priceField = item[0];
    },
    // 删除奖品
    deletePrice(item) {
      // 保存一个副本，解决数据渲染问题
      const copy = this.rewardDTOList.slice(0);
      this.$confirm("确定删除所选信息?", "", {
        type: "warning",
      })
        .then(() => {
          for (let i = 0; i < item.length; i++) {
            item[i].deleted = 1;
            this.deletePriceList.push(item[i]);
            const index = this.getIndex(item[i]);
            if (index > -1) {
              copy.splice(index, 1);
            }
          }
          this.rewardDTOList = copy;
          this.$message.success("删除成功");
        })
        .catch(() => {});
    },
    getIndex(val) {
      let idx = -1;
      try {
        this.rewardDTOList.forEach((item, index) => {
          if (item.pid === val.pid) {
            idx = index;
            throw new Error("stop");
          }
        });
      } catch (e) {}
      return idx;
    },
    // 添加报名信息字段
    addSignUpField(item) {
      console.log(item);
      for (let i = 0; i < this.fieldDTOList.length; i++) {
        if (this.fieldDTOList[i].fieldName == item.fieldName) {
          this.$message.error("该字段已经存在");
          return;
        }
      }
      if (item.fieldName == "选择队长") {
        this.$message.error("该字段无法添加");
        return;
      }
      this.fieldDTOList.push(item);
    },

    // 删除报名信息字段
    deleteSignUpField(index) {
      this.$confirm("确定删除该字段?", "", {
        type: "warning",
      })
        .then(() => {
          const item = this.fieldDTOList[index];
          // 删除fieldDTOList中的对应item，将对应item的deleted字段置为1，保存在deleteFieldDTOList
          item.deleted = 1;
          this.deleteFieldDTOList.push(item);
          this.fieldDTOList.splice(index, 1);
        })
        .catch(() => {});
    },
    cancel() {
      this.dialogFormVisible = false;
    },
    submit(title) {
      // 验证不通过返回
      if (!this.$refs.pirceForm.validate()) {
        return;
      }
      console.log(this.$refs.pirceForm.validate());
      if (title !== "修改奖品信息") {
        // 当前表单中的数据
        const item = this.$refs.pirceForm.returnField();
        // console.log(item);
        this.rewardDTOList.push(item);
        // console.log(this.rewardDTOList)
      } else {
        // 匹配对应的id进行修改
        {
          const field = this.$refs.pirceForm.returnField();
          try {
            this.rewardDTOList.map((item, index) => {
              if (item.pid === field.pid) {
                item.rewardLevel = field.rewardLevel;
                item.rewardName = field.rewardName;
                item.number = field.number;
                throw new Error("stopIteration");
              }
            });
          } catch (e) {}
        }
      }
      this.dialogFormVisible = false;
    },
    // 添加活动内容模块
    addActivityModule() {
      this.publicData.activityContentList.push({
        aid: Math.floor(Math.random() * 100000),
        title: "",
        content: "",
      });
    },
    // 删除活动内容模块
    deleteActivityModule(index) {
      this.$confirm("确定删除该活动内容?", "", {
        type: "warning",
      })
        .then(() => {
          const item = this.publicData.activityContentList[index];
          // 新增为空的情况不处理，直接删除
          if (item.title !== "" && item.content !== "") {
            item.deleted = 1;
            this.deleteActivityContentList.push(item);
          }
          this.publicData.activityContentList.splice(index, 1);
        })
        .catch(() => {});
    },
    // 清除缓存
    clearStore() {
      this.$store.commit("setActivityData", null);
      this.reload();
    },
  },
  // 清除编辑页面跳转到发布页面时存在的数据回显
  beforeDestroy() {
    if (this.$route.params.id) {
      this.publicData = {};
    }
  },
};
</script>

<style lang="scss" scoped>
@import "~@/assets/css/variables";

.activity-create-container {
  width: 100%;
  min-height: 100%;
  background-color: #f5f7fa;
  display: flex;
  flex-direction: column;

  // 页面头部样式
  .page-header {
    background-color: #fff;
    border-bottom: 1px solid #ebeef5;
    padding: 0 20px;

    .header-content {
      max-width: 1200px;
      margin: 0 auto;
      padding: 16px 0;
      display: flex;
      align-items: center;

      .back-btn {
        margin-right: 16px;
        font-size: 14px;

        &:hover {
          color: $color-primary;
        }
      }

      .page-title {
        h1 {
          font-size: 22px;
          font-weight: 600;
          margin: 0;
          color: #303133;
        }

        .page-subtitle {
          font-size: 14px;
          color: #909399;
          margin-top: 4px;
        }
      }
    }
  }

  // 主要内容区域
  .main-content {
    flex: 1;
    max-width: 1200px;
    margin: 0 auto;
    padding: 24px 20px;
    width: calc(100% - 40px); // 减去左右padding
    box-sizing: border-box;

    // 进度指示器
    .progress-steps {
      display: flex;
      align-items: center;
      justify-content: center;
      margin-bottom: 32px;
      background: rgba(255, 255, 255, 0.95);
      backdrop-filter: blur(10px);
      border-radius: 12px;
      padding: 16px 24px;
      box-shadow: 0 2px 12px rgba(0, 0, 0, 0.1);
      z-index: 1000;
      position: relative;
      width: calc(100% - 48px); // 与父容器宽度一致，减去左右padding
      min-width: 400px; // 确保有足够宽度显示所有步骤

      // 浮窗状态 - 保持原有样式和宽度
      &.floating {
        position: fixed;
        top: 100px;
        left: 75%;
        transform: translateX(-50%);
        box-shadow: 0 8px 32px rgba(0, 0, 0, 0.25);
        border: 1px solid rgba(255, 255, 255, 0.2);
        z-index: 9999;
        background: rgba(255, 255, 255, 0.98);
        backdrop-filter: blur(20px);
        margin-bottom: 0;
        animation: slideDown 0.3s ease;
        // 保持与原始状态相同的宽度和布局
        width: 400px; // 视窗宽度减去左右边距
        display: flex;
        align-items: center;
        justify-content: center;
        padding: 16px 24px;
        border-radius: 12px;
      }

      @keyframes slideDown {
        from {
          opacity: 0;
          transform: translateX(-50%) translateY(-20px);
        }
        to {
          opacity: 1;
          transform: translateX(-50%) translateY(0);
        }
      }

      // 浮窗状态下确保子元素样式不变
      &.floating {
        .step {
          display: flex;
          flex-direction: column;
          align-items: center;
          position: relative;
          cursor: pointer;
          padding: 8px;
          border-radius: 8px;
          transition: all 0.3s ease;

          .step-icon {
            width: 36px;
            height: 36px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-weight: 600;
            margin-bottom: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
          }

          .step-title {
            font-size: 14px;
            transition: all 0.3s ease;
          }
        }

        .step-line {
          flex: 1;
          height: 2px;
          margin: 0 12px;
          margin-bottom: 24px;
          transition: all 0.3s ease;
        }
      }

      .step {
        display: flex;
        flex-direction: column;
        align-items: center;
        position: relative;
        cursor: pointer;
        padding: 8px;
        border-radius: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: rgba($color-primary, 0.05);
          transform: translateY(-2px);
        }

        .step-icon {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background-color: #ebeef5;
          color: #909399;
          display: flex;
          align-items: center;
          justify-content: center;
          font-weight: 600;
          margin-bottom: 8px;
          transition: all 0.3s ease;
          font-size: 14px;

          i {
            font-size: 16px;
          }
        }

        .step-title {
          font-size: 14px;
          color: #606266;
          transition: all 0.3s ease;
        }

        &.active {
          .step-icon {
            background-color: $color-primary;
            color: #fff;
            box-shadow: 0 4px 12px rgba($color-primary, 0.3);
          }

          .step-title {
            color: $color-primary;
            font-weight: 500;
          }
        }

        &.completed {
          .step-icon {
            background-color: $color-success;
            color: #fff;
            box-shadow: 0 4px 12px rgba($color-success, 0.3);
          }

          .step-title {
            color: $color-success;
            font-weight: 500;
          }
        }
      }

      .step-line {
        flex: 1;
        height: 2px;
        background-color: #ebeef5;
        margin: 0 12px;
        margin-bottom: 24px;
        transition: all 0.3s ease;

        &.completed {
          background-color: $color-success;
          box-shadow: 0 1px 4px rgba($color-success, 0.3);
        }
      }
    }

    // 表单区域
    .form-sections {
      .form-section {
        background-color: #fff;
        border-radius: 8px;
        box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);
        margin-bottom: 24px;
        overflow: hidden;

        .section-header {
          display: flex;
          align-items: center;
          padding: 16px 24px;
          border-bottom: 1px solid #ebeef5;

          .section-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            background-color: rgba($color-primary, 0.1);
            color: $color-primary;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 20px;
            margin-right: 16px;
          }

          .section-title {
            h2 {
              font-size: 18px;
              font-weight: 600;
              margin: 0;
              color: #303133;
            }

            p {
              font-size: 14px;
              color: #909399;
              margin-top: 4px;
            }
          }
        }

        .section-content {
          padding: 24px;
        }
      }
    }
  }

  // 底部操作栏
  .footer-actions {
    background-color: #fff;
    border-top: 1px solid #ebeef5;
    padding: 16px 20px;
    margin-top: 24px;

    .actions-content {
      max-width: 1200px;
      margin: 0 auto;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .right-actions {
        display: flex;
        gap: 12px;
      }
    }
  }
}

// 响应式适配
@media screen and (max-width: 768px) {
  .activity-create-container {
    .page-header {
      .header-content {
        padding: 12px 0;

        .page-title {
          h1 {
            font-size: 18px;
          }
        }
      }
    }

    .main-content {
      padding: 16px 12px;

      .progress-steps {
        margin-bottom: 24px;

        .step {
          .step-icon {
            width: 32px;
            height: 32px;
            font-size: 14px;
          }

          .step-title {
            font-size: 12px;
          }
        }
      }

      .form-sections {
        .form-section {
          .section-header {
            padding: 12px 16px;

            .section-icon {
              width: 32px;
              height: 32px;
              font-size: 16px;
            }

            .section-title {
              h2 {
                font-size: 16px;
              }

              p {
                font-size: 12px;
              }
            }
          }

          .section-content {
            padding: 16px;
          }
        }
      }
    }

    .footer-actions {
      padding: 12px 16px;
    }
  }
}

// 移动端横屏提醒样式
:global(.mobile-orientation-message) {
  // 靠左放置
  left: 20px !important;
  right: auto !important;
  transform: none !important;
  margin: 0 !important;

  // 自定义颜色 - 浅蓝色主题
  background: #e0f2fe !important;
  border: 1px solid #0284c7 !important;
  color: #0c4a6e !important;
  box-shadow: 0 4px 16px rgba(2, 132, 199, 0.2) !important;

  .el-message__content {
    font-size: 14px;
    line-height: 1.4;
    padding: 8px 0;
    color: #0c4a6e !important;
    font-weight: 500;
  }

  .el-message__icon {
    font-size: 16px;
    color: #0284c7 !important;
  }

  .el-message__closeBtn {
    font-size: 14px;
    top: 50%;
    transform: translateY(-50%);
    color: #0284c7 !important;

    &:hover {
      color: #0369a1 !important;
    }
  }

  // 移动端样式优化
  @media (max-width: 768px) {
    left: 16px !important;
    max-width: calc(100% - 32px);
    border-radius: 12px;

    .el-message__content {
      font-size: 13px;
      padding: 6px 0;
    }

    .el-message__icon {
      font-size: 14px;
    }
  }

  // 小屏手机样式
  @media (max-width: 480px) {
    left: 12px !important;
    max-width: calc(100% - 24px);
    border-radius: 10px;

    .el-message__content {
      font-size: 12px;
    }

    .el-message__icon {
      font-size: 13px;
    }
  }
}

// 警告类型的横屏提醒 - 浅橙色主题
:global(.mobile-orientation-message.el-message--warning) {
  background: #fef3c7 !important;
  border: 1px solid #f59e0b !important;
  color: #92400e !important;
  box-shadow: 0 4px 16px rgba(245, 158, 11, 0.2) !important;

  .el-message__icon {
    color: #f59e0b !important;
  }

  .el-message__closeBtn {
    color: #f59e0b !important;

    &:hover {
      color: #d97706 !important;
    }
  }
}

// 信息类型的横屏提醒 - 保持浅蓝色主题
:global(.mobile-orientation-message.el-message--info) {
  background: #e0f2fe !important;
  border: 1px solid #0284c7 !important;
  color: #0c4a6e !important;
  box-shadow: 0 4px 16px rgba(2, 132, 199, 0.2) !important;

  .el-message__icon {
    color: #0284c7 !important;
  }

  .el-message__closeBtn {
    color: #0284c7 !important;

    &:hover {
      color: #0369a1 !important;
    }
  }
}
</style>
