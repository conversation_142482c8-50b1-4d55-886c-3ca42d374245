<template>
  <div class="form">
    <el-form
      ref="publicForm"
      :model="publicData"
      label-width="120px"
      :rules="rules"
    >
      <el-form-item label="活动主题" prop="actName">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-input v-model="publicData.actName" placeholder="请输入活动主题" />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="活动类型" prop="actType">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-radio-group v-model="publicData.actType">
              <el-radio
                v-for="actType in AllActType"
                :key="actType.sort"
                :label="actType.sort"
                :style="getRadioStyle(actType.label)"
              >
                <span>{{ actType.label }}</span>
              </el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="报名时间" required>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="enlistStartTime">
              <el-date-picker
                type="datetime"
                placeholder="选择报名开始时间"
                v-model="publicData.enlistStartTime"
                style="width: 100%"
                :picker-options="enlistStartTimeOption"
              />
            </el-form-item>
          </el-col>
          <el-col class="line" :span=".5">-</el-col>
          <el-col :span="8">
            <el-form-item prop="enlistEndTime">
              <el-date-picker
                type="datetime"
                placeholder="选择报名结束时间"
                v-model="publicData.enlistEndTime"
                style="width: 100%"
                :picker-options="enlistEndTimeOption"
                @change="handleTimeChange('enlistEnd')"
              />
        </el-form-item>
      </el-col>
    </el-row>
      </el-form-item>
      <el-form-item label="报名人数" prop="actNum">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              :min="1"
              type="number"
              v-model.number="publicData.actNum"
              placeholder="请输入报名人数"
            />
          </el-col>
        </el-row>
      </el-form-item >
       <el-form-item label="是否携带亲属">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-radio-group v-if="publicData.activityDetail" v-model="publicData.activityDetail.isAllowedBring" :disabled="isEnlistStartTime">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
        <!-- 每人携带人数设置，暂时设置bringRelativesNum -->
        <el-row v-if="publicData.activityDetail && publicData.activityDetail.isAllowedBring === 1">
           <el-form-item style="paddingTop: 20px; color: grey" prop="activityDetail.bringRelativesNum">
              <el-row>
                <el-col :span="2">亲属人数</el-col>
                <el-col :span="8">
                  <el-input
                    :min="1"
                    type="number"
                    v-model.number="publicData.activityDetail.bringRelativesNum"
                    placeholder="请输入允许携带亲属人数"
                    />
                </el-col>
              </el-row>
            </el-form-item>
        </el-row>
      </el-form-item>
      <el-form-item label="是否可候补">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-radio-group v-if="publicData.activityDetail" v-model="publicData.activityDetail.isAllowedAlternate" :disabled="isEnlistStartTime">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
        <el-row v-if="publicData.activityDetail && publicData.activityDetail.isAllowedAlternate === 1">
           <el-form-item style="paddingTop: 20px; color: grey">
              <el-row>
                <el-col :span="2">可候补人数</el-col>
                <el-col :span="8">
                   <el-input
                  :min="1"
                  type="number"
                  v-model.number="publicData.activityDetail.allowedAlternateNum"
                  placeholder="请输入可候补人数"
                  />
                </el-col>
              </el-row>
            </el-form-item>
        </el-row>
      </el-form-item>
      <el-form-item label="活动头图">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-upload
              v-loading="uploadImageLoading"
              class="imageUploader"
              :action="action"
              :headers="headers"
              :show-file-list="false"
              :before-upload="beforeUploadHandle"
              :with-credentials="true"
              :on-success="handleUploadSuccess('image')"
            >
              <img
                v-if="publicData.activityImage !== ''"
                :src="publicData.activityImage"
                class="imageContainer"
              >
              <i v-else class="el-icon-plus imageUploader-icon"></i>
            </el-upload>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="报名费用" prop="activityDetail.enlistCost">
        <el-row :gutter="20">
          <el-col :span="8">
            <el-input
              :min="0"
              v-if="publicData.activityDetail"
              v-model.number="publicData.activityDetail.enlistCost"
              type="number"
              placeholder="请输入报名费用"
            />
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="活动时间" required>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item prop="actStartTime">
              <el-date-picker
                type="datetime"
                placeholder="选择活动开始时间"
                v-model="publicData.actStartTime"
                style="width: 100%"
                :picker-options="actStartTimeOption"
                @change="handleTimeChange('actStart')"
              />
            </el-form-item>
          </el-col>
          <el-col class="line" :span=".5">-</el-col>
          <el-col :span="8">
            <el-form-item prop="actEndTime">
              <el-date-picker
                type="datetime"
                placeholder="选择活动结束时间"
                v-model="publicData.actEndTime"
                style="width: 100%"
                :picker-options="actEndTimeOption"
                @change="handleTimeChange('actEnd')"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form-item>
      <el-form-item label="活动地址" prop="address">
        <el-row :gutter="20">
          <el-col :span="16">
            <el-input
              v-model="publicData.address"
              placeholder="请输入活动地址"
            />
          </el-col>
        </el-row>
      </el-form-item>
      <!-- <el-form-item label="是否需要审核">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-radio-group v-if="publicData.activityDetail" v-model="publicData.activityDetail.isExamine">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
      </el-form-item> -->
       <el-form-item label="是否自动发通知">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-radio-group v-if="publicData.activityDetail" v-model="publicData.activityDetail.isSignupNotice">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
      </el-form-item>
       <el-form-item label="是否每日通知">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-radio-group v-if="publicData.activityDetail" v-model="publicData.activityDetail.isNoticeCreator">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
      </el-form-item>
      <!-- <el-form-item label="协议" prop="contract">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-upload
              class="upload-demo"
              drag
              :action="action"
              :with-credentials="true"
              :limit="1"
              :on-success="handleUploadSuccess('contract')"
              :file-list="agreement"
              :on-remove="handleRemove1"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </el-col>
        </el-row>
      </el-form-item> -->
      <!-- <el-form-item label="附件" prop="appendix">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-upload
              class="upload-demo"
              drag
              :action="action"
              :with-credentials="true"
              :limit="1"
              :on-success="handleUploadSuccess('appendix')"
              :file-list="enclosure"
              :on-remove="handleRemove2"
            >
              <i class="el-icon-upload"></i>
              <div class="el-upload__text">
                将文件拖到此处，或<em>点击上传</em>
              </div>
            </el-upload>
          </el-col>
        </el-row>
      </el-form-item> -->
      <el-form-item label="是否有抽奖">
        <el-row :gutter="20">
          <el-col :span="12">
            <el-radio-group v-if="publicData.activityDetail" v-model="publicData.activityDetail.isLuckDraw">
              <el-radio :label="1">是</el-radio>
              <el-radio :label="0">否</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
        <!-- 是否显示抽奖奖品列表 -->
        <el-row v-if="publicData.activityDetail && publicData.activityDetail.isLuckDraw === 1">
          <data-table
            style="marginTop: 20px"
            :isShowPagination="false"
            isShowSelector
            :buttonList="buttonList"
            :tableData="rewardDTOList"
            @addPriceData="addPriceData"
            @modifyPriceData="modifyPriceData"
            @deletePriceData="deletePriceData"
            :tableTitle="tableTitle"
          />
        </el-row>
      </el-form-item>
      <el-form-item label="组队模式">
        <el-row :gutter="20">
          <el-col :span="24">
            <el-radio-group v-if="publicData.activityDetail" v-model="publicData.activityDetail.isTeam" :disabled="isEnlistStartTime">
              <el-radio :label="0">不组队</el-radio>
              <el-radio :label="1">队长组队</el-radio>
              <el-radio :label="2">部门组队</el-radio>
              <el-radio :label="3">自由组队</el-radio>
            </el-radio-group>
          </el-col>
        </el-row>
        <!-- 是否显示选择组队方式设置 -->
        <el-row v-if=" publicData.activityDetail && publicData.activityDetail.isTeam !== 0">
            <el-form-item style="paddingTop: 20px; color: grey">
              <el-row :gutter="20">
                <el-col :span="2">队伍人数</el-col>
                <el-col :span="5">
                  <el-form-item >
                  <el-input
                    placeholder="最少人数"
                    :min="1"
                    :max="1000"
                    v-model.number="publicData.activityDetail.teamPlayersMin"
                    type="number"
                  />
                  </el-form-item>
                </el-col>
                <el-col class="line" :span="0.5">—</el-col>
                <el-col :span="5">
                  <el-form-item prop="activityDetail.teamPlayersMax">
                  <el-input
                    v-model.number="publicData.activityDetail.teamPlayersMax"
                    type="number"
                    placeholder="最多人数"
                  />
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form-item>
            <el-form-item style="marginTop: 20px" v-if=" publicData.activityDetail && publicData.activityDetail.isTeam === 1">
              <el-row>
                <el-col :span="2">选择队长</el-col>
                <el-col :span="8">
                  <el-select
                    filterable
                    v-model="selectCaptains"
                    @visible-change="selectCaps"
                    multiple
                    @remove-tag="removeLeaderTag"
                    placeholder="请选择"
                  >
                    <el-option
                      v-for="item in captainList"
                      :key="item.teamLeaderId"
                      :label="item.teamLeaderName"
                      :value="item.teamLeaderId"
                    />
                  </el-select>
                </el-col>
              </el-row>
            </el-form-item>
            <!-- 暂时命名为selectDepartments，selectDeparts， departmentTree-->
            <el-form-item style="marginTop: 20px" v-if=" publicData.activityDetail && publicData.activityDetail.isTeam === 2">
              <el-row>
                <el-col :span="2">选择部门</el-col>
                <el-col :span="8">
                  <!-- departmentTree部门树 -->
                  <el-cascader
                  v-model="selectDepartments"
                  :props="casProps"
                  :options="departmentTree"
                  :show-all-levels="false"
                   filterable
                  clearable
                  @visible-change="selectDeparts"
                  @remove-tag="removeDepartTag"
                  />
                </el-col>
              </el-row>
            </el-form-item>
        </el-row>
      </el-form-item>
    </el-form>
  </div>
</template>

<script>
import { getAllActType } from '@/api/Type';
import stringToColor from 'string-to-color';

export default {
  props: {
    publicData: {
      type: Object,
      require: true
    },
    captainList: {
      type: Array,
      default() {
        return [];
      }
    },
    departmentTree: {
      type: Array,
      default() {
        return [];
      }
    },
    allDepartmentList: {
      type: Array,
      default() {
        return [];
      }
    },
    rewardDTOList: {
      type: Array,
      default() {
        return [];
      }
    },
    rules: {
      type: Object,
      default() {
        return {};
      }
    },
    selectedCaptains: {
      type: Array,
      default() {
        return [];
      }
    },
    oldDepartsArrary: {
      type: Array,
      default() {
        return [];
      }
    }
  },
  data() {
    return {
      // 活动类型数据
      AllActType: [],
      // 暂时选中得部门列表
      selectDepartments: [],
      // 部门级联选择器配置
      casProps: {
        value: 'id',
        label: 'name',
        // 实现多选
        multiple: true,
        // 通过 props.checkStrictly = true 来设置父子节点取消选中关联，从而达到选择任意一级选项的目的
        checkStrictly: true
      },
      selectCaptains: [],
      action: '',
      headers: {},
      tableTitle: [
        { label: '奖项', prop: 'rewardLevel' },
        { label: '奖品', prop: 'rewardName' },
        { label: '份数', prop: 'number' }
      ],
      buttonList: [
        {
          text: '新增',
          event: 'addPriceData',
          type: 'primary'
        },
        {
          text: '修改',
          limit: 1,
          only: 1,
          event: 'modifyPriceData'
        },
        {
          text: '删除',
          limit: 1,
          type: 'danger',
          event: 'deletePriceData'
        }
      ],
      enlistStartTime: this.publicData.enlistStartTime,
      enlistEndTime: this.publicData.enlistEndTime,
      actStartTime: this.publicData.actStartTime,
      actEndTime: this.publicData.actEndTime,
      // 禁用过去时间
      enlistStartTimeOption: {
        disabledDate: (time) => {
          // // 时间限制在发布后的明天，无法计算管理员发布信息所需要的时间，因此限制在一天后
          // return time.getTime() < Date.now() - (8.64e7 - 3600 * 24 * 1000);
          // sb需求要求可以当天发布
          // return time.getTime() < Date.now() - 8.64e7;
          // 若先选择了报名结束时间后，限制只能选择今天--报名结束时间范围
          if (this.publicData.enlistEndTime) {
            return time.getTime() > (typeof this.publicData.enlistEndTime !== 'object' ? (new Date(this.publicData.enlistEndTime)).getTime() : this.publicData.enlistEndTime)
            || time.getTime() < Date.now() - 8.64e7;
          }
          // 默认限制为今天以及今天之后
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      // TODO 可以将时间判断整合一下
      enlistEndTimeOption: {
        disabledDate: (time) => {
          if (this.publicData && this.publicData.enlistStartTime) {
            //  return time.getTime() < (typeof this.publicData.enlistStartTime !== 'object' ? (new Date(this.publicData.enlistStartTime)).getTime() : this.publicData.enlistStartTime);
            const temporaryTime = (typeof this.publicData.enlistStartTime !== 'object' ? (new Date(this.publicData.enlistStartTime)) : this.publicData.enlistStartTime);
            // 根据所选时间限制时间范围
            if (temporaryTime.getHours() >= 12) {
              return time.getTime() < temporaryTime.getTime() - 8.64e7;
            }
            return time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
          }
          // 默认设置为1天后
          return time.getTime() < Date.now();
        }
      },
      // 活动开始时间不能小于当前时间以及所选择的活动报名时间
      actStartTimeOption: {
        disabledDate: (time) => {
          // 若先选择了活动结束时间后，限制只能选择报名时间--活动结束时间范围
          if (this.publicData.actEndTime) {
            if (this.publicData.enlistEndTime) {
            // 编辑时获取的数据是 string格式
            // return time.getTime() < (typeof this.publicData.enlistEndTime !== 'object' ? (new Date(this.publicData.enlistEndTime)).getTime() : this.publicData.enlistEndTime);
              const temporaryTime = (typeof this.publicData.enlistEndTime !== 'object' ? (new Date(this.publicData.enlistEndTime)) : this.publicData.enlistEndTime);
              if (temporaryTime.getHours() >= 12) {
                return time.getTime() > (typeof this.publicData.actEndTime !== 'object' ? (new Date(this.publicData.actEndTime)).getTime() : this.publicData.actEndTime)
                || time.getTime() < temporaryTime.getTime() - 8.64e7;
              }
              return time.getTime() > (typeof this.publicData.actEndTime !== 'object' ? (new Date(this.publicData.actEndTime)).getTime() : this.publicData.actEndTime)
              || time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
            } else if (this.publicData.enlistStartTime) {
            //  return time.getTime() < (typeof this.publicData.enlistStartTime !== 'object' ? (new Date(this.publicData.enlistStartTime)).getTime() : this.publicData.enlistStartTime);
              const temporaryTime = (typeof this.publicData.enlistStartTime !== 'object' ? (new Date(this.publicData.enlistStartTime)) : this.publicData.enlistStartTime);
              if (temporaryTime.getHours() >= 12) {
                return time.getTime() > (typeof this.publicData.actEndTime !== 'object' ? (new Date(this.publicData.actEndTime)).getTime() : this.publicData.actEndTime)
                || time.getTime() < temporaryTime.getTime() - 8.64e7;
              }
              return time.getTime() > (typeof this.publicData.actEndTime !== 'object' ? (new Date(this.publicData.actEndTime)).getTime() : this.publicData.actEndTime)
              || time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
            }
            return time.getTime() > (typeof this.publicData.actEndTime !== 'object' ? (new Date(this.publicData.actEndTime)).getTime() : this.publicData.actEndTime)
            || time.getTime() < Date.now() - 8.64e7;
          } else if (this.publicData.enlistEndTime) {
            // 编辑时获取的数据是 string格式
            // return time.getTime() < (typeof this.publicData.enlistEndTime !== 'object' ? (new Date(this.publicData.enlistEndTime)).getTime() : this.publicData.enlistEndTime);
            const temporaryTime = (typeof this.publicData.enlistEndTime !== 'object' ? (new Date(this.publicData.enlistEndTime)) : this.publicData.enlistEndTime);
            if (temporaryTime.getHours() >= 12) {
              return time.getTime() < temporaryTime.getTime() - 8.64e7;
            }
            return time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
          } else if (this.publicData.enlistStartTime) {
            const temporaryTime = (typeof this.publicData.enlistStartTime !== 'object' ? (new Date(this.publicData.enlistStartTime)) : this.publicData.enlistStartTime);
            if (temporaryTime.getHours() >= 12) {
              return time.getTime() < temporaryTime.getTime() - 8.64e7;
            }
            return time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
          }
          return time.getTime() < Date.now() - 8.64e7;
        }
      },
      // 活动结束时间不能小于当前时间以及活动开始、活动报名时间
      actEndTimeOption: {
        disabledDate: (time) => {
          // 活动开始时间已选时
          if (this.publicData.actStartTime) {
            // return time.getTime() < (typeof this.publicData.actStartTime !== 'object' ? (new Date(this.publicData.actStartTime)).getTime() : this.publicData.actStartTime);
            const temporaryTime = (typeof this.publicData.actStartTime !== 'object' ? (new Date(this.publicData.actStartTime)) : this.publicData.actStartTime);
            if (temporaryTime.getHours() >= 12) {
              return time.getTime() < temporaryTime.getTime() - 8.64e7;
            }
            return time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
          } else if (this.publicData.enlistEndTime) {
            // return time.getTime() < (typeof this.publicData.enlistEndTime !== 'object' ? (new Date(this.publicData.enlistEndTime)).getTime() : this.publicData.enlistEndTime);
            const temporaryTime = (typeof this.publicData.enlistEndTime !== 'object' ? (new Date(this.publicData.enlistEndTime)) : this.publicData.enlistEndTime);
            if (temporaryTime.getHours() >= 12) {
              return time.getTime() < temporaryTime.getTime() - 8.64e7;
            }
            return time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
          } else if (this.publicData.enlistStartTime) {
            // return time.getTime() < (typeof this.publicData.actStartTime !== 'object' ? (new Date(this.publicData.actStartTime)).getTime() : this.publicData.actStartTime);
            const temporaryTime = (typeof this.publicData.enlistStartTime !== 'object' ? (new Date(this.publicData.enlistStartTime)) : this.publicData.enlistStartTime);
            if (temporaryTime.getHours() >= 12) {
              return time.getTime() < temporaryTime.getTime() - 8.64e7;
            }
            return time.getTime() < temporaryTime.getTime() - 3600 * 12 * 1000;
          }
          // 默认设置为1天后
          return time.getTime() < Date.now();
        }
      },
      agreement: [],
      enclosure: [],
      flag: true,
      // 用于图片回显，图片上传后返回及保存的只是部分url，需要进行拼接
      // activityImage: this.publicData.activityImage,
      // 在图片上传时调用，避免上传时间过久导致无图片回显
      uploadImageLoading: false,
      // 根据当前时间与活动开始报名时间，判断是否已经开始报名
      isEnlistStartTime: null,
      type:0
    };
  },
  async created() {
    this.selectCaptains = this.selectedCaptains;
    this.selectDepartments = this.oldDepartsArrary;
    this.action = '/api/file/upload';
<<<<<<< HEAD

    // 获取活动类型数据
    try {
      const response = await getAllActType();
      this.AllActType = response.data.data;
    } catch (error) {
      console.error("获取活动类型失败：", error);
    }
=======
>>>>>>> bfda4301 (fix:更新代码，去除百度jquery文件，修复部分人无法登录问题。)
  },
  watch: {
    oldDepartsArrary() {
      this.selectDepartments = this.oldDepartsArrary;
    },
    // 只监听一次，用于回显已上传文件， 取消监听，防止重选文件后不显示
    publicData: {
      deep: true,
      handler(val) {
        if (this.flag) {
          this.flag = false;
          this.agreement = this.publicData.activityDetail && this.publicData.activityDetail.agreement !== '' ? [{ name: '协议', url: this.publicData.activityDetail.agreement }] : [];
          this.enclosure = this.publicData.activityDetail && this.publicData.activityDetail.enclosure !== '' ? [{ name: '附件', url: this.publicData.activityDetail.enclosure }] : [];
          this.publicData.activityImage = this.publicData.actImg !== '' ? `/image/${this.publicData.actImg}` : '';
          this.isEnlistStartTime = this.publicData.status === 2;
        }
      }
    }
  },
  methods: {
    // 获取活动类型描述
    getTypeDescription(typeName) {
      const descriptions = {
        '徒步活动': '户外徒步、登山等运动类活动',
        '自定义活动': '会议、培训、聚会等其他类型活动',
        '团建活动': '团队建设、拓展训练等集体活动',
        '培训会议': '学习培训、工作会议等知识分享',
        '体育运动': '球类运动、健身活动等体育项目',
        '文化娱乐': '文艺演出、娱乐活动等文化项目',
        '志愿服务': '公益活动、社区服务等志愿项目',
        '学术交流': '学术研讨、技术交流等专业活动'
      };
      return descriptions[typeName] || '其他类型活动';
    },

    // 获取单选框样式
    getRadioStyle(typeName) {
      const baseColor = stringToColor(typeName);
      return {
        '--type-color': baseColor,
        '--type-bg-color': `${baseColor}20`,
        '--type-border-color': baseColor
      };
    },

    // 获取图标样式
    getIconStyle(typeName) {
      const baseColor = stringToColor(typeName);
      return {
        color: baseColor
      };
    },

    // 判断时间，结束时间都不能小于开始时间，活动时间都不能小于报名时间
    handleTimeChange(flag) {
      switch (flag) {
        case 'enlistEnd':
          if (this.publicData.enlistEndTime !== '' && this.publicData.enlistEndTime - this.publicData.enlistStartTime <= 0) {
            this.$message.error('报名结束时间不能早于或等于开始时间');
            this.publicData.enlistEndTime = '';
          }
          break;
        case 'actStart':
          if (this.publicData.actStartTime !== '' && (this.publicData.actStartTime - this.publicData.enlistStartTime <= 0 || this.publicData.actStartTime - this.publicData.enlistEndTime <= 0)) {
            this.$message.error('活动开始时间不能早于或等于报名时间');
            this.publicData.actStartTime = '';
          }
          break;
        case 'actEnd':
          if (this.publicData.actEndTime !== '' && this.publicData.actEndTime - this.publicData.actStartTime <= 0) {
            this.$message.error('活动结束时间不能早于或等于开始时间');
            this.publicData.actEndTime = '';
          }
          if (this.publicData.actEndTime !== '' && (this.publicData.actEndTime - this.publicData.enlistStartTime <= 0 || this.publicData.actEndTime - this.publicData.enlistEndTime <= 0)) {
            this.$message.error('活动结束时间不能早于或等于报名时间');
            this.publicData.actEndTime = '';
          }
          break;
        default: break;
      }
    },
    beforeUploadHandle(file) {
      if (file.type !== 'image/jpg' && file.type !== 'image/jpeg' && file.type !== 'image/png') {
        this.$message.error('只支持jpg、png格式的图片！');
        return false;
      }
      this.uploadImageLoading = true;
    },
    // 当移除时默认就置空原来的url
    handleRemove1() {
      if (this.publicData.activityDetail) {
        this.publicData.activityDetail.agreement = '';
      }
    },
    handleRemove2() {
      if (this.publicData.activityDetail) {
        this.publicData.activityDetail.enclosure = '';
      }
    },
    // 根据id匹配对应的name后组成一个对象，返回数组
    selectCaps() {
      const list = [];
      if (this.selectCaptains.length !== 0) {
        this.selectCaptains.forEach(item => {
          try {
            this.captainList.forEach(cap => {
              if (cap.id === item) {
                list.push({ teamLeaderName: cap.name, teamLeaderId: item, deleted: 0 });
                throw new Error('stopIteration');
              }
            });
          } catch (e) {}
        });
      }
      this.$emit('getCaps', list);
    },
    // 移除队长tag
    removeLeaderTag() {
      this.selectCaps();
    },
    // 移除部门tag
    removeDepartTag() {
      this.selectDeparts();
    },
    // 部门选择
    selectDeparts() {
      console.log(this.selectDepartments);
      const list = [];
      if (this.selectDepartments.length !== 0) {
        const selectList = [];
        // 提取出选中部门的id组成数组
        this.selectDepartments.forEach(item => {
          selectList.push(item[item.length - 1]);
        });
        console.log(selectList);
        // 将部门数组转换成对象数组
        selectList.forEach(item => {
          this.allDepartmentList.forEach(cap => {
            if (cap.id === item) {
              list.push({ teamName: cap.name, departId: cap.id, deleted: 0 });
            }
          });
        });
        console.log(list);
      }
      this.$emit('getDepts', list);
    },
    handleUploadSuccess(type) {
      return (res, file) => {
        if (res.status === 0) {
          //  上传的文件url修改成不拼接ip，展示的才需要拼接
          switch (type) {
            case 'image':
              // 保存图片上传至服务器的路径
              this.publicData.actImg = res.data;
              // 对图片的地址进行拼接
              this.publicData.activityImage = `/image/${res.data}`;
              this.uploadImageLoading = false;
              break;
            case 'contract':
              this.publicData.activityDetail.agreement = res.data;
              break;
            case 'appendix':
              this.publicData.activityDetail.enclosure = res.data;
              break;
            default:
              break;
          }
        } else {
          this.$message.error('上传失败');
        }
      };
    },
    beforeRemove(file) {
      return this.$confirm(`确定移除 ${file.name}？`);
    },
    addPriceData(item) {
      this.$emit('addPrice', item);
    },
    modifyPriceData(item) {
      this.$emit('modifyPrice', item);
    },
    deletePriceData(item) {
      this.$emit('deletePrice', item);
    },
    verify() {
      let status = null;
      // let status = true;
      // try {
      //   await this.$refs.publicForm.validate();
      //   status = true;
      //   alert(11111);
      // } catch (error) {
      //   status = false;
      // }
      this.$refs.publicForm.validate((valid) => {
        console.log('valid', valid);
        if (valid) {
          console.log(valid);
          status = true;
        } else {
          console.log(valid);
          status = false;
        }
      });
      return status;
    }
  }
  // beforeDestroy() {
  //   this.activityImage = '';
  // }
};
</script>

<style lang="scss" scoped>
.form {
  margin-top: 40px;
}
.imageUploader,
.el-upload {
  border: 1px dashed #d9d9d9;
  border-radius: 6px;
  width: 178px;
  cursor: pointer;
  position: relative;
  overflow: hidden;
}
.imageUploader,
.el-upload:hover {
  border-color: #d9d9d9;
}
.imageUploader-icon {
  font-size: 28px;
  color: #8c939d;
  width: 178px;
  height: 178px;
  line-height: 178px;
  text-align: center;
}
.imageContainer {
  width: 178px;
  max-height: 178px;
  object-fit: fill;
  z-index: 99;
}

// 覆盖队伍人数标签的宽度限制 - 使用更高特异性的选择器
.form ::v-deep .el-row .el-col.el-col-2 {
  width: auto;
  flex: 0 0 auto;
  max-width: none;
}


</style>
