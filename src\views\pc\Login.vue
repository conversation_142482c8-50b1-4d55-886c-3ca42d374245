<template>
  <!-- <div > -->
    <el-container class="login">
      <el-header style="margin-top: 80px">
        <div class="title-font">
          九联服务号活动发布端
        </div>
      </el-header>
      <el-main>
        <div class="login-box">
          <div class="small-box">
            <div class="title-font">欢迎登录</div>
            <el-form class="login-form" :rules="rules" :model="loginForm" ref="loginForm" >
              <el-form-item prop="account">
                <el-input
                  type="text"
                  class="user"
                  clearable
                  placeholder="请输入手机号"
                  v-model="loginForm.account"/>
              </el-form-item>
              <el-form-item prop="password">
                <el-input
                  type="text"
                  class="password"
                  placeholder="密码"
                  v-model="loginForm.password"
                  show-password/>
              </el-form-item>
            <el-button class="login_btn" @click="submitLogin('loginForm')">登录</el-button>
            </el-form>
          </div>
        </div>
      </el-main>
    </el-container>
  <!-- </div> -->
</template>

<script>
import { LoginPc } from '../../api/Login/index';
export default {
  name: 'Login',
  data() {
    const validatePass = (rule, value, callback) => {
      if (!value) {
        callback(new Error('密码不能为空'));
      } else {
        callback();
      }
    };
    return {
      loginForm: {
        account: '',
        password: ''
      },
      value: '',
      rules: {
        account: [
          { required: true, message: '请输入手机号', trigger: 'change' },
          { pattern: /^1[345789]\d{9}$/, message: '账号格式错误', trigger: 'blur' }
        ],
        password: [
          { required: true, validator: validatePass, trigger: 'blur' }
        ]
      }
    };
  },
  methods: {
    submitLogin(forName) {
      this.$refs[forName].validate((valid) => {
        if (valid) {
          LoginPc({
            username: this.loginForm.account,
            password: this.loginForm.password
          }).then((res) => {
            if (res.data && res.data.message === '成功') {
              this.$message({
                type: 'success',
                message: '登录成功'
              });
              const userInfo = res.data.data.user.name;
              window.sessionStorage.setItem('userInfo', userInfo);
              this.$router.push('/Home/public');
            } else {
              this.$message({
                type: 'error',
                message: res.data.message || '登录失败'
              });
            }
          }).catch((error) => {
            this.$message({
              type: 'error',
              message: '登录失败，请稍后重试'
            });
          });
        } else {
          this.$message({
            type: 'error',
            message: '格式错误，请重新填写'
          });
        }
      });
    }
  }
};
</script>

<style scoped>
.login {
  background-image: url('../../assets/images/1.jpg');
  background-size:100% 100%;
  display: flex;
  position: fixed;
  flex-direction: column;
  align-content: center;
  align-items: center;
  width: 100%;
  height: 100%;
}
.title-font {
  color: #fff;
  text-align: center;
  font-size: 30px;
  padding-top: 20px;
  padding-bottom: 20px;
}
.login-box {
  width: 584px;
  /* height: 360px; */
  display: flex;
  flex-direction: column;
  align-items: center;
  /* padding: 50px; */
}
.small-box {
  width: 440px;
  height: 300px;
  background-color:rgba(0,0,0,0.2);
}
.user{
  color: white;
  width: 220px;
}
.password {
  color: white;
  width: 220px;
}
.login_btn{
  width: 244px;
  height: 35px;
  background: hsl(198deg 100% 23%);
  color: #fff;
}
.login_btn /deep/ .el-button:focus, .el-button:hover {
  color: hsl(210deg 100% 63%);
  border-color: hsl(0deg 0% 100% / 50%);
  background-color: hsl(220deg 3% 19%);
}
.login-form{
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
}
.el-icon-arrow-down {
  font-size: 12px;
}
.tip {
  height: 60px;
  width: 274px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
.font-question{
  font-size: 10px;
  color: #fff;
}
.tip /deep/ .el-input__inner {
  height: 30px;
  width: 150px;
}
/* 错误提示 */
.login-form /deep/ .el-form-item__error {
  color: hsl(0deg 87% 69%);
  font-size: 12px;
  line-height: 1;
  padding-top: 4px;
  position: absolute;
  top: 30%;
  left: 142px;
}
</style>
